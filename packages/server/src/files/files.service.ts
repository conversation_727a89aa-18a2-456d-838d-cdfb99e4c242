import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Response } from 'express';
import { createReadStream } from 'fs';
import { stat } from 'fs/promises';
import * as path from 'path';

@Injectable()
export class FilesService {
  // Return a simple descriptor for the stored file
  buildFileResponse(file: Express.Multer.File, folder?: string) {
    if (!file) throw new BadRequestException('File is required');
    const filePath = folder ? path.join(folder, file.filename) : file.filename;

    return {
      originalName: file.originalname,
      filename: file.filename,
      mimeType: file.mimetype,
      size: file.size,
      path: path.relative(process.cwd(), file.path),
      url: `/uploads/${filePath}`,
    };
  }

  buildFilesResponse(files: Express.Multer.File[], folder?: string) {
    if (!files || files.length === 0)
      throw new BadRequestException('Files is required');
    return files.map((f) => this.buildFileResponse(f, folder));
  }

  async serveFile(filename: string, res: Response): Promise<void> {
    const filePath = path.join(process.cwd(), 'uploads', filename);

    try {
      const fileStat = await stat(filePath);
      if (!fileStat.isFile()) {
        throw new NotFoundException('File not found');
      }
    } catch {
      throw new NotFoundException('File not found');
    }

    // Stream the file
    const fileStream = createReadStream(filePath);
    fileStream.pipe(res);

    // Handle stream errors
    fileStream.on('error', () => {
      throw new NotFoundException('Error reading file');
    });
  }
}
