import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { IsPublic } from 'src/auth/decoratots/auth.decorator';
import { FilesService } from './files.service';
import { UploadRequest } from './types';

@Controller('files')
export class FilesController {
  constructor(private filesService: FilesService) {}

  @IsPublic()
  @Get('*')
  async serveFile(@Req() req: any, @Res() res: Response) {
    // Extract the file path from the request URL, removing the '/files/' prefix
    const filePath = req.params[0] || req.url.replace('/files/', '');
    console.log('filePath', filePath);
    return this.filesService.serveFile(filePath, res);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  uploadSingle(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: UploadRequest,
  ) {
    return this.filesService.buildFileResponse(file, req.body?.folder);
  }

  @Post('uploads')
  @UseInterceptors(FilesInterceptor('files', 10))
  uploadMultiple(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: UploadRequest,
  ) {
    return this.filesService.buildFilesResponse(files, req.body?.folder);
  }
}
