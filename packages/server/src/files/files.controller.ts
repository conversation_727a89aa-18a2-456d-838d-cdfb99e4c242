import {
  <PERSON>,
  Get,
  Param,
  Post,
  Req,
  Res,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { IsPublic } from 'src/auth/decoratots/auth.decorator';
import { FilesService } from './files.service';
import { UploadRequest } from './types';

@Controller('files')
export class FilesController {
  constructor(private filesService: FilesService) {}

  @IsPublic()
  @Get(':filename')
  async serveFile(
    @Param('filename') filename: string,
    @Res() res: Response,
  ): Promise<void> {
    return this.filesService.serveFile(filename, res);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  uploadSingle(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: UploadRequest,
  ) {
    return this.filesService.buildFileResponse(file, req.body?.folder);
  }

  @Post('uploads')
  @UseInterceptors(FilesInterceptor('files', 10))
  uploadMultiple(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: UploadRequest,
  ) {
    return this.filesService.buildFilesResponse(files, req.body?.folder);
  }
}
