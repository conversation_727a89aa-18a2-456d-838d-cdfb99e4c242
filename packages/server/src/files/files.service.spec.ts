import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Response } from 'express';
import { createReadStream } from 'fs';
import { stat } from 'fs/promises';
import * as path from 'path';
import { FilesService } from './files.service';

// Mock fs modules
jest.mock('fs');
jest.mock('fs/promises');

describe('FilesService', () => {
  let service: FilesService;
  let mockResponse: Partial<Response>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FilesService],
    }).compile();

    service = module.get<FilesService>(FilesService);

    mockResponse = {
      pipe: jest.fn(),
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('serveFile', () => {
    it('should serve a file from root uploads directory', async () => {
      const filename = 'test-file.jpg';
      const mockStat = { isFile: () => true };
      const mockStream = { pipe: jest.fn(), on: jest.fn() };

      (stat as jest.Mock).mockResolvedValue(mockStat);
      (createReadStream as jest.Mock).mockReturnValue(mockStream);

      await service.serveFile(filename, mockResponse as Response);

      expect(stat).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filename),
      );
      expect(createReadStream).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filename),
      );
      expect(mockStream.pipe).toHaveBeenCalledWith(mockResponse);
    });

    it('should serve a file from nested folder', async () => {
      const filePath = 'documents/test-file.pdf';
      const mockStat = { isFile: () => true };
      const mockStream = { pipe: jest.fn(), on: jest.fn() };

      (stat as jest.Mock).mockResolvedValue(mockStat);
      (createReadStream as jest.Mock).mockReturnValue(mockStream);

      await service.serveFile(filePath, mockResponse as Response);

      expect(stat).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filePath),
      );
      expect(createReadStream).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filePath),
      );
    });

    it('should throw BadRequestException for directory traversal attempts', async () => {
      const maliciousPath = '../../../etc/passwd';

      await expect(
        service.serveFile(maliciousPath, mockResponse as Response),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for absolute paths', async () => {
      const absolutePath = '/etc/passwd';

      await expect(
        service.serveFile(absolutePath, mockResponse as Response),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException when file does not exist', async () => {
      const filename = 'nonexistent-file.jpg';

      (stat as jest.Mock).mockRejectedValue(new Error('File not found'));

      await expect(
        service.serveFile(filename, mockResponse as Response),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when path is not a file', async () => {
      const filename = 'directory-name';
      const mockStat = { isFile: () => false };

      (stat as jest.Mock).mockResolvedValue(mockStat);

      await expect(
        service.serveFile(filename, mockResponse as Response),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('buildFileResponse', () => {
    it('should build correct response for file in root directory', () => {
      const mockFile = {
        originalname: 'test.jpg',
        filename: 'test-123.jpg',
        mimetype: 'image/jpeg',
        size: 1024,
        path: '/app/uploads/test-123.jpg',
      } as Express.Multer.File;

      const result = service.buildFileResponse(mockFile);

      expect(result).toEqual({
        originalName: 'test.jpg',
        filename: 'test-123.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: expect.any(String),
        url: '/files/test-123.jpg',
      });
    });

    it('should build correct response for file in nested folder', () => {
      const mockFile = {
        originalname: 'document.pdf',
        filename: 'document-456.pdf',
        mimetype: 'application/pdf',
        size: 2048,
        path: '/app/uploads/documents/document-456.pdf',
      } as Express.Multer.File;

      const result = service.buildFileResponse(mockFile, 'documents');

      expect(result).toEqual({
        originalName: 'document.pdf',
        filename: 'document-456.pdf',
        mimeType: 'application/pdf',
        size: 2048,
        path: expect.any(String),
        url: '/files/documents/document-456.pdf',
      });
    });
  });
});
