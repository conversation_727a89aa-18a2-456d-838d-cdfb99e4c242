import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Response } from 'express';
import { createReadStream } from 'fs';
import { stat } from 'fs/promises';
import * as path from 'path';
import { FilesService } from './files.service';

// Mock fs modules
jest.mock('fs');
jest.mock('fs/promises');

describe('FilesService', () => {
  let service: FilesService;
  let mockResponse: Partial<Response>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FilesService],
    }).compile();

    service = module.get<FilesService>(FilesService);
    
    mockResponse = {
      pipe: jest.fn(),
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('serveFile', () => {
    it('should serve a file from root uploads directory', async () => {
      const filename = 'test-file.jpg';
      const mockStat = { isFile: () => true };
      const mockStream = { pipe: jest.fn(), on: jest.fn() };

      (stat as jest.Mock).mockResolvedValue(mockStat);
      (createReadStream as jest.Mock).mockReturnValue(mockStream);

      await service.serveFile(filename, mockResponse as Response);

      expect(stat).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filename)
      );
      expect(createReadStream).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filename)
      );
      expect(mockStream.pipe).toHaveBeenCalledWith(mockResponse);
    });

    it('should serve a file from nested folder', async () => {
      const filePath = 'documents/test-file.pdf';
      const mockStat = { isFile: () => true };
      const mockStream = { pipe: jest.fn(), on: jest.fn() };

      (stat as jest.Mock).mockResolvedValue(mockStat);
      (createReadStream as jest.Mock).mockReturnValue(mockStream);

      await service.serveFile(filePath, mockResponse as Response);

      expect(stat).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filePath)
      );
      expect(createReadStream).toHaveBeenCalledWith(
        path.join(process.cwd(), 'uploads', filePath)
      );
    });

    it('should throw BadRequestException for directory traversal attempts', async () => {
      const maliciousPath = '../../../etc/passwd';

      await expect(
        service.serveFile(maliciousPath, mockResponse as Response)
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for absolute paths', async () => {
      const absolutePath = '/etc/passwd';

      await expect(
        service.serveFile(absolutePath, mockResponse as Response)
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException when file does not exist', async () => {
      const filename = 'nonexistent-file.jpg';

      (stat as jest.Mock).mockRejectedValue(new Error('File not found'));

      await expect(
        service.serveFile(filename, mockResponse as Response)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when path is not a file', async () => {
      const filename = 'directory-name';
      const mockStat = { isFile: () => false };

      (stat as jest.Mock).mockResolvedValue(mockStat);

      await expect(
        service.serveFile(filename, mockResponse as Response)
      ).rejects.toThrow(NotFoundException);
    });
  });
});
